import React, { useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { Toaster } from 'react-hot-toast'
import useAuthStore from './store/authStore'
import useNotificationStore from './store/notificationStore'
import webSocketService from './lib/websocket'
import useTokenRefresh from './hooks/useTokenRefresh'
import Layout from './components/layout/Layout'
import LandingPage from './components/pages/LandingPage'
import SearchPage from './components/pages/SearchPage'
import ShopPage from './components/pages/ShopPage'
import AppointmentBookingPage from './components/pages/AppointmentBookingPage'
import UserDetailsPage from './components/pages/UserDetailsPage'
import DashboardRouter from './components/DashboardRouter'

import ShopCreationWizard from './components/shop/ShopCreationWizard'
import AppointmentManagement from './components/appointments/AppointmentManagement'
import './App.css'

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
})

// Protected Route Component - for authenticated users only
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated } = useAuthStore()
  return isAuthenticated ? children : <Navigate to="/login" />
}

// Employee/Owner Route Component - for dashboard access
const EmployeeRoute = ({ children }) => {
  const { isAuthenticated, isEmployee, isOwner } = useAuthStore()
  return isAuthenticated && (isEmployee() || isOwner()) ? children : <Navigate to="/" />
}



function App() {
  const { initializeAuth, isAuthenticated, token, user, _hasHydrated } = useAuthStore()
  const { initialize: initializeNotifications, disconnectWebSocket } = useNotificationStore()

  // Use token refresh hook for automatic token management
  useTokenRefresh()

  useEffect(() => {
    // Only initialize auth after Zustand has rehydrated
    if (!_hasHydrated) {
      return
    }

    const initAuth = async () => {
      try {
        await initializeAuth()
      } catch (error) {
        console.error('Auth initialization failed:', error)
      }
    }

    initAuth()
  }, [initializeAuth, _hasHydrated])

  useEffect(() => {
    // Connect to WebSocket when authenticated
    if (isAuthenticated && token && user?.id) {
      // Add a small delay to ensure auth state is fully settled
      const timer = setTimeout(() => {
        webSocketService.connect(token)
          .then(() => {
            // Initialize notifications after WebSocket connection
            initializeNotifications(user.id, token)
          })
          .catch(error => {
            console.error('WebSocket connection failed:', error)
          })
      }, 100) // Reduced delay since we're using auth store token directly

      return () => clearTimeout(timer)
    } else {
      // Disconnect WebSocket when not authenticated
      if (webSocketService.isConnected()) {
        webSocketService.disconnect()
        disconnectWebSocket()
      }
    }
  }, [isAuthenticated, token, user?.id, initializeNotifications, disconnectWebSocket])

  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <div className="App">
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
              },
              success: {
                duration: 3000,
                theme: {
                  primary: 'green',
                  secondary: 'black',
                },
              },
            }}
          />
          <Routes>
            {/* Public Routes - No authentication required */}
            <Route
              path="/"
              element={
                <Layout>
                  <LandingPage />
                </Layout>
              }
            />
            <Route
              path="/search"
              element={
                <Layout>
                  <SearchPage />
                </Layout>
              }
            />
            <Route
              path="/shop/:id"
              element={
                <Layout>
                  <ShopPage />
                </Layout>
              }
            />
            <Route
              path="/shop/:id/book"
              element={
                <Layout>
                  <AppointmentBookingPage />
                </Layout>
              }
            />

            {/* Legacy Auth Routes - Redirect to home (auth is now modal-based) */}
            <Route path="/login" element={<Navigate to="/" />} />
            <Route path="/register" element={<Navigate to="/" />} />

            {/* Protected Routes - Authentication required */}
            <Route
              path="/profile"
              element={
                <ProtectedRoute>
                  <Layout>
                    <UserDetailsPage />
                  </Layout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/create-shop"
              element={
                <ProtectedRoute>
                  <Layout>
                    <ShopCreationWizard />
                  </Layout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/appointments"
              element={
                <ProtectedRoute>
                  <Layout>
                    <AppointmentManagement />
                  </Layout>
                </ProtectedRoute>
              }
            />

            {/* Employee/Owner Routes - Role-based access */}
            <Route
              path="/dashboard"
              element={
                <EmployeeRoute>
                  <Layout>
                    <DashboardRouter />
                  </Layout>
                </EmployeeRoute>
              }
            />



            {/* Catch all route */}
            <Route path="*" element={<Navigate to="/" />} />
          </Routes>
        </div>
      </Router>
    </QueryClientProvider>
  )
}

export default App
