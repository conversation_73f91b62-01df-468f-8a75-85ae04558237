import { useEffect, useRef } from 'react'
import useAuthStore from '../store/authStore'

const useTokenRefresh = () => {
  const { isAuthenticated, isTokenValid, refreshToken, logout } = useAuthStore()
  const intervalRef = useRef(null)

  useEffect(() => {
    if (isAuthenticated) {
      // Check token validity every 5 minutes
      intervalRef.current = setInterval(async () => {
        try {
          // First do a client-side check
          if (!isTokenValid()) {
            const refreshed = await refreshToken()

            if (!refreshed) {
              // Don't logout immediately on periodic check failure
              // The API interceptor will handle this when actual API calls fail
            }
          }
        } catch (error) {
          // Don't logout on periodic check errors
        }
      }, 5 * 60 * 1000) // 5 minutes

      // Also check immediately when the hook mounts (but don't logout on failure)
      const checkToken = async () => {
        if (!isTokenValid()) {
          try {
            await refreshToken()
          } catch (error) {
            // Don't logout here - let the API interceptor handle it
          }
        }
      }
      checkToken()
    } else {
      // Clear interval if not authenticated
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }
    }

    // Cleanup on unmount
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [isAuthenticated, isTokenValid, refreshToken, logout])

  // Also check token on page focus
  useEffect(() => {
    const handleFocus = async () => {
      if (isAuthenticated && !isTokenValid()) {
        try {
          await refreshToken()
        } catch (error) {
          // Don't logout here - let the API interceptor handle it
        }
      }
    }

    window.addEventListener('focus', handleFocus)
    return () => window.removeEventListener('focus', handleFocus)
  }, [isAuthenticated, isTokenValid, refreshToken])
}

export default useTokenRefresh
