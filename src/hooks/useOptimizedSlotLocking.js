import { useState, useCallback, useEffect, useRef } from 'react'
import { appointmentAPI } from '../lib/api'

/**
 * Optimized slot locking hook that minimizes backend calls and handles guest users properly
 */
const useOptimizedSlotLocking = (shopId, serviceId, employeeId) => {
  const [lockedSlots, setLockedSlots] = useState(new Set())
  const [userLocks, setUserLocks] = useState(new Map()) // Track user's own locks
  const lockTimersRef = useRef(new Map()) // Track expiry timers

  // Generate storage key for a slot
  const getStorageKey = useCallback((dateTime) => {
    return `lockToken-${shopId}-${serviceId}-${employeeId}-${dateTime}`
  }, [shopId, serviceId, employeeId])

  // Store lock token with expiry information
  const storeLockToken = useCallback((dateTime, lockToken, expiresInMinutes = 15) => {
    const expiryTime = new Date(Date.now() + expiresInMinutes * 60 * 1000)
    const tokenData = {
      token: lockToken,
      expiryTime: expiryTime.toISOString(),
      createdAt: new Date().toISOString(),
      slotDateTime: dateTime
    }
    
    const storageKey = getStorageKey(dateTime)
    sessionStorage.setItem(storageKey, JSON.stringify(tokenData))
    
    // Track user's own lock
    setUserLocks(prev => new Map(prev).set(dateTime, {
      token: lockToken,
      expiryTime,
      storageKey
    }))

    // Set up expiry timer
    const timeoutId = setTimeout(() => {
      releaseLock(dateTime, 'expired')
    }, expiresInMinutes * 60 * 1000)

    lockTimersRef.current.set(dateTime, timeoutId)
  }, [getStorageKey])

  // Get stored lock token
  const getStoredLockToken = useCallback((dateTime) => {
    const storageKey = getStorageKey(dateTime)
    const tokenDataStr = sessionStorage.getItem(storageKey)
    
    if (!tokenDataStr) return null

    try {
      const tokenData = JSON.parse(tokenDataStr)
      const expiryTime = new Date(tokenData.expiryTime)
      const now = new Date()

      if (now > expiryTime) {
        sessionStorage.removeItem(storageKey)
        return null
      }

      return tokenData.token
    } catch {
      // Legacy format
      return tokenDataStr
    }
  }, [getStorageKey])

  // Lock a slot
  const lockSlot = useCallback(async (dateTime) => {
    try {
      
      const response = await appointmentAPI.lockSlot({
        shopId,
        serviceId,
        employeeId,
        dateTime
      })

      const lockToken = response.lockToken || response.data?.lockToken
      const expiresIn = response.expiresIn || response.data?.expiresIn || 15

      if (lockToken) {
        storeLockToken(dateTime, lockToken, expiresIn)
        return { success: true, lockToken, expiresIn }
      } else {
        return { success: false, error: 'No lock token received' }
      }
    } catch (error) {
      console.error('❌ Failed to lock slot:', error)
      return { 
        success: false, 
        error: error.response?.data?.message || 'Failed to lock slot' 
      }
    }
  }, [shopId, serviceId, employeeId, storeLockToken])

  // Release a lock
  const releaseLock = useCallback(async (dateTime, reason = 'manual') => {
    const userLock = userLocks.get(dateTime)
    if (!userLock) {
      return
    }

    try {

      // Clear timer
      const timerId = lockTimersRef.current.get(dateTime)
      if (timerId) {
        clearTimeout(timerId)
        lockTimersRef.current.delete(dateTime)
      }

      // Remove from session storage
      sessionStorage.removeItem(userLock.storageKey)

      // Remove from user locks
      setUserLocks(prev => {
        const newMap = new Map(prev)
        newMap.delete(dateTime)
        return newMap
      })

      // Call backend to release lock
      await appointmentAPI.unlockSlot({
        shopId,
        serviceId,
        employeeId,
        dateTime,
        lockToken: userLock.token
      })

    } catch (error) {
      console.error('❌ Failed to release lock:', error)
      // Don't throw error for cleanup operations
    }
  }, [shopId, serviceId, employeeId, userLocks])

  // Release payment lock (for payment failures)
  const releasePaymentLock = useCallback(async (dateTime, reason = 'payment_failure') => {
    const userLock = userLocks.get(dateTime)
    if (!userLock) {
      return
    }

    try {

      // Clear timer
      const timerId = lockTimersRef.current.get(dateTime)
      if (timerId) {
        clearTimeout(timerId)
        lockTimersRef.current.delete(dateTime)
      }

      // Remove from session storage
      sessionStorage.removeItem(userLock.storageKey)

      // Remove from user locks
      setUserLocks(prev => {
        const newMap = new Map(prev)
        newMap.delete(dateTime)
        return newMap
      })

      // Call backend payment lock release endpoint
      await appointmentAPI.releasePaymentLock({
        shopId,
        serviceId,
        employeeId,
        dateTime,
        lockToken: userLock.token
      })

    } catch (error) {
      console.error('❌ Failed to release payment lock:', error)
      // Don't throw error for cleanup operations
    }
  }, [shopId, serviceId, employeeId, userLocks])

  // Check if user has a valid lock for a slot
  const hasValidLock = useCallback((dateTime) => {
    const userLock = userLocks.get(dateTime)
    if (!userLock) return false

    const now = new Date()
    return now <= userLock.expiryTime
  }, [userLocks])

  // Clean up expired locks
  const cleanupExpiredLocks = useCallback(() => {
    const now = new Date()
    const expiredSlots = []

    userLocks.forEach((lock, dateTime) => {
      if (now > lock.expiryTime) {
        expiredSlots.push(dateTime)
      }
    })

    expiredSlots.forEach(dateTime => {
      releaseLock(dateTime, 'cleanup_expired')
    })

    if (expiredSlots.length > 0) {
    }
  }, [userLocks, releaseLock])

  // Handle real-time slot updates
  const handleSlotUpdate = useCallback((update) => {
    const { action, dateTime } = update

    if (action === 'LOCKED') {
      setLockedSlots(prev => new Set([...prev, dateTime]))
    } else if (action === 'UNLOCKED') {
      setLockedSlots(prev => {
        const newSet = new Set(prev)
        newSet.delete(dateTime)
        return newSet
      })
    }
  }, [])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Clear all timers
      lockTimersRef.current.forEach(timerId => clearTimeout(timerId))
      lockTimersRef.current.clear()
    }
  }, [])

  // Periodic cleanup
  useEffect(() => {
    const cleanupInterval = setInterval(cleanupExpiredLocks, 30000) // Every 30 seconds
    return () => clearInterval(cleanupInterval)
  }, [cleanupExpiredLocks])

  return {
    lockedSlots,
    userLocks,
    lockSlot,
    releaseLock,
    releasePaymentLock,
    hasValidLock,
    getStoredLockToken,
    handleSlotUpdate,
    cleanupExpiredLocks
  }
}

export default useOptimizedSlotLocking
