import { useEffect, useCallback, useRef, useState } from 'react'
import webSocketService from '../lib/websocket'
import useAuthStore from '../store/authStore'

/**
 * Optimized hook for subscribing to real-time slot updates with persistent connection
 * @param {string} shopId - Shop ID
 * @param {string} serviceId - Service ID
 * @param {string} employeeId - Employee ID (optional)
 * @param {string} date - Date in YYYY-MM-DD format
 * @param {function} onSlotUpdate - Callback function for slot updates
 * @returns {object} - { isConnected, isSubscribed, connectionError }
 */
const useSlotUpdates = (shopId, serviceId, employeeId, date, onSlotUpdate) => {
  const { token } = useAuthStore()
  const [isConnected, setIsConnected] = useState(false)
  const [isSubscribed, setIsSubscribed] = useState(false)
  const [connectionError, setConnectionError] = useState(null)

  const unsubscribeRef = useRef(null)
  const connectionCheckRef = useRef(null)
  const subscriptionTopicRef = useRef(null)

  // Stable connection management
  const ensureConnection = useCallback(async () => {
    if (webSocketService.isConnected()) {
      setIsConnected(true)
      setConnectionError(null)
      return true
    }

    try {
      await webSocketService.connect(token)
      setIsConnected(true)
      setConnectionError(null)
      return true
    } catch (error) {
      console.error('Failed to connect WebSocket:', error)
      setIsConnected(false)
      setConnectionError(error.message)
      return false
    }
  }, [token])

  const subscribe = useCallback(async () => {
    if (!shopId || !serviceId || !date || !onSlotUpdate) {
      console.warn('Missing required parameters for slot updates subscription')
      return false
    }

    if (isSubscribed && subscriptionTopicRef.current) {
      return true
    }

    // Ensure WebSocket connection
    const connected = await ensureConnection()
    if (!connected) {
      console.error('Cannot subscribe - WebSocket connection failed')
      return false
    }

    // Create topic based on whether employee is specified
    let topic
    if (employeeId) {
      // Most specific: shop + service + employee + date
      topic = `slots.${shopId}.${serviceId}.${employeeId}.${date}`
    } else {
      // Less specific: shop + service + date (any employee)
      topic = `slots.${shopId}.${serviceId}.${date}`
    }

    try {
      // Subscribe to slot updates with enhanced error handling
      const subscribedTopic = webSocketService.subscribe(topic, (message) => {
        // Handle different message formats
        let slotUpdate = message
        if (message.data && message.data.type === 'SLOT_UPDATE') {
          slotUpdate = message.data
        } else if (message.type === 'SLOT_UPDATE') {
          slotUpdate = message
        }

        // Only process if it's a slot update
        if (slotUpdate.type === 'SLOT_UPDATE') {
          onSlotUpdate(slotUpdate)
        }
      })

      // Store the topic for unsubscription
      unsubscribeRef.current = subscribedTopic
      subscriptionTopicRef.current = topic
      setIsSubscribed(true)

      return true
    } catch (error) {
      console.error('Failed to subscribe to slot updates:', error)
      setIsSubscribed(false)
      return false
    }
  }, [shopId, serviceId, employeeId, date, onSlotUpdate, ensureConnection])

  const unsubscribe = useCallback(() => {
    if (unsubscribeRef.current) {
      try {
        webSocketService.unsubscribe(unsubscribeRef.current)
        unsubscribeRef.current = null
        subscriptionTopicRef.current = null
        setIsSubscribed(false)
      } catch (error) {
        console.warn('Failed to unsubscribe from WebSocket topic:', error)
      }
    }
  }, [])

  // Connection monitoring and auto-reconnection
  useEffect(() => {
    const checkConnection = () => {
      const connected = webSocketService.isConnected()
      setIsConnected(connected)

      if (!connected && isSubscribed) {
        setIsSubscribed(false)
        // Try to reconnect and resubscribe
        ensureConnection().then(success => {
          if (success && shopId && serviceId && date && onSlotUpdate) {
            subscribe()
          }
        })
      }
    }

    // Check connection immediately
    checkConnection()

    // Set up periodic connection check
    connectionCheckRef.current = setInterval(checkConnection, 5000) // Check every 5 seconds

    return () => {
      if (connectionCheckRef.current) {
        clearInterval(connectionCheckRef.current)
      }
    }
  }, [ensureConnection, subscribe, shopId, serviceId, date, onSlotUpdate, isSubscribed])

  // Auto-subscribe when parameters change (but only when not already subscribed)
  useEffect(() => {
    if (shopId && serviceId && date && onSlotUpdate && !isSubscribed) {
      subscribe()
    }

    // Cleanup on unmount or when key parameters change
    return () => {
      if (subscriptionTopicRef.current) {
        unsubscribe()
      }
    }
  }, [shopId, serviceId, employeeId, date]) // Removed onSlotUpdate and subscribe to prevent frequent re-subscriptions

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      unsubscribe()
      if (connectionCheckRef.current) {
        clearInterval(connectionCheckRef.current)
      }
    }
  }, [unsubscribe])

  return {
    subscribe,
    unsubscribe,
    isConnected,
    isSubscribed,
    connectionError
  }
}

export default useSlotUpdates
