import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import useAuthStore from '../store/authStore'
import { shopAPI, analyticsAPI, appointmentAPI, employeeAPI } from '../lib/api'
import { Button } from './ui/Button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/Card'
import ShopCreationModal from './shop/ShopCreationModal'
import ShopManagement from './shop/ShopManagement'
import {
  Building,
  Users,
  Calendar,
  DollarSign,
  TrendingUp,
  Settings,
  Plus,
  Eye,
  Edit,
  BarChart3
} from 'lucide-react'

const OwnerDashboard = () => {
  const { user, isOwner, refreshToken } = useAuthStore()
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const [showShopCreationModal, setShowShopCreationModal] = useState(false)
  const [currentView, setCurrentView] = useState('dashboard') // 'dashboard' or 'management'

  // Ensure user has owner role when component loads
  useEffect(() => {
    const checkOwnerRole = async () => {
      if (!isOwner()) {
        try {
          await refreshToken()
          if (!isOwner()) {
            navigate('/dashboard')
          }
        } catch (error) {
          console.error('OwnerDashboard: Error refreshing token:', error)
        }
      }
    }
    checkOwnerRole()
  }, [isOwner, refreshToken, navigate])



  const handleCreateShop = () => {
    setShowShopCreationModal(true)
  }

  const handleShopCreationSuccess = (data) => {
    setShowShopCreationModal(false)

    // Show success message and refresh the page to get updated data
    alert('🎉 New shop created successfully!')

    // Refresh the page to ensure we have the latest data
    window.location.reload()
  }

  const handleViewAppointments = () => {
    // Navigate to appointments page or show appointments modal
  }

  const handleManageEmployees = () => {
    // Navigate to employees page or show employees modal
  }

  const handleViewAnalytics = () => {
    // Navigate to analytics page or show analytics modal
  }

  const handleViewShop = (shopId) => {
    // Navigate to shop details page
  }

  const handleEditShop = (shopId) => {
    // Navigate to shop edit page or show edit modal
  }

  const handleManageShop = (shopId) => {
    // Switch to shop management view
    setCurrentView('management')
  }

  // Fetch owner's shops
  const { data: shopsData, isLoading: shopsLoading, error: shopsError } = useQuery({
    queryKey: ['ownerShops'],
    queryFn: async () => {
      try {
        const response = await shopAPI.getOwnerShops()
        return response
      } catch (error) {
        console.error('OwnerDashboard: Error fetching owner shops:', error)
        console.error('OwnerDashboard: Error response:', error.response?.data)
        console.error('OwnerDashboard: Error status:', error.response?.status)
        throw error
      }
    },
    enabled: !!user && isOwner(),
    retry: 1,
    onError: (error) => {
      console.error('OwnerDashboard: Query onError callback:', error)
    }
  })

  const shops = Array.isArray(shopsData?.data) ? shopsData.data : Array.isArray(shopsData) ? shopsData : []

  // Fetch owner analytics data
  const { data: analyticsData, isLoading: analyticsLoading, error: analyticsError } = useQuery({
    queryKey: ['ownerAnalytics'],
    queryFn: async () => {
      try {
        const response = await analyticsAPI.getOwnerAnalytics()
        return response.data
      } catch (error) {
        console.error('OwnerDashboard: Error fetching analytics:', error)
        // Return mock data if analytics endpoint is not available yet
        return {
          totalRevenue: '$0',
          totalAppointments: 0,
          totalCustomers: 0,
          averageRating: 0.0,
          totalShops: shops.length
        }
      }
    },
    enabled: !!user && isOwner() && shops.length > 0,
    retry: 1
  })

  // Fetch recent appointments for all shops
  const { data: recentAppointments, isLoading: appointmentsLoading } = useQuery({
    queryKey: ['ownerRecentAppointments', shops.map(s => s.id)],
    queryFn: async () => {
      if (shops.length === 0) return []

      try {
        // Fetch appointments for the first shop (or combine from all shops)
        const firstShop = shops[0]
        const response = await appointmentAPI.getShopAppointments(firstShop.id, {
          page: 0,
          size: 5,
          sort: 'appointmentDateTime,desc'
        })
        return response.data?.content || []
      } catch (error) {
        console.error('OwnerDashboard: Error fetching appointments:', error)
        return []
      }
    },
    enabled: !!user && isOwner() && shops.length > 0,
    retry: 1
  })



  // Show shop management view if selected
  if (currentView === 'management') {
    return (
      <div className="min-h-screen bg-gray-50">
        <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            <ShopManagement />
          </div>
        </main>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          
          {/* Welcome Section */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Welcome to your Business Dashboard
            </h2>
            <p className="text-gray-600">
              Manage your beauty business, track performance, and grow your customer base.
            </p>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                {analyticsLoading ? (
                  <div className="text-2xl font-bold">Loading...</div>
                ) : (
                  <div className="text-2xl font-bold">{analyticsData?.totalRevenue || '$0'}</div>
                )}
                <p className="text-xs text-muted-foreground">
                  {analyticsData?.revenueGrowth ? `${analyticsData.revenueGrowth}% from last month` : 'No data available'}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Appointments</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                {analyticsLoading ? (
                  <div className="text-2xl font-bold">Loading...</div>
                ) : (
                  <div className="text-2xl font-bold">{analyticsData?.totalAppointments || 0}</div>
                )}
                <p className="text-xs text-muted-foreground">
                  {analyticsData?.appointmentGrowth ? `${analyticsData.appointmentGrowth}% from last month` : 'No data available'}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Customers</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                {analyticsLoading ? (
                  <div className="text-2xl font-bold">Loading...</div>
                ) : (
                  <div className="text-2xl font-bold">{analyticsData?.totalCustomers || 0}</div>
                )}
                <p className="text-xs text-muted-foreground">
                  {analyticsData?.customerGrowth ? `${analyticsData.customerGrowth}% from last month` : 'No data available'}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Rating</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                {analyticsLoading ? (
                  <div className="text-2xl font-bold">Loading...</div>
                ) : (
                  <div className="text-2xl font-bold">{analyticsData?.averageRating?.toFixed(1) || '0.0'}</div>
                )}
                <p className="text-xs text-muted-foreground">
                  ⭐ Average rating
                </p>
              </CardContent>
            </Card>
          </div>

          {/* My Shops Section */}
          <div className="mb-8">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-semibold text-gray-900">My Shops</h3>
              <Button
                className="bg-gray-900 hover:bg-gray-800 text-white"
                onClick={handleCreateShop}
              >
                <Plus className="w-4 h-4 mr-2" />
                Add New Shop
              </Button>
            </div>

            {shopsLoading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
                <p className="mt-2 text-gray-600">Loading your shops...</p>
              </div>
            ) : shopsError ? (
              <Card className="border-red-200 bg-red-50">
                <CardContent className="pt-6">
                  <h4 className="text-lg font-medium text-red-800 mb-2">Error Loading Shops</h4>
                  <p className="text-red-600 mb-2">
                    {shopsError.response?.data?.message || shopsError.message || 'Failed to load shops'}
                  </p>
                  <div className="text-sm text-red-600 mb-4">
                    <p><strong>Status:</strong> {shopsError.response?.status || 'Unknown'}</p>
                    <p><strong>User Role:</strong> {isOwner() ? 'OWNER' : 'NOT OWNER'}</p>
                    <p><strong>User ID:</strong> {user?.id || 'Not available'}</p>
                  </div>
                  <Button
                    onClick={() => window.location.reload()}
                    className="bg-red-600 hover:bg-red-700 text-white"
                  >
                    Retry
                  </Button>
                </CardContent>
              </Card>
            ) : shops.length === 0 ? (
              <Card className="border-dashed border-2 border-gray-300">
                <CardContent className="pt-6 text-center">
                  <Building className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h4 className="text-lg font-medium text-gray-900 mb-2">No shops yet</h4>
                  <p className="text-gray-600 mb-4">
                    Get started by creating your first beauty business.
                  </p>
                  <Button
                    className="bg-gray-900 hover:bg-gray-800 text-white"
                    onClick={handleCreateShop}
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Create Your First Shop
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {shops.map((shop) => (
                  <Card key={shop.id} className="hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <CardTitle className="flex items-center justify-between">
                        <span>{shop.name}</span>
                        <Building className="w-5 h-5 text-gray-400" />
                      </CardTitle>
                      <CardDescription>
                        {shop.businessTypes?.join(', ') || 'Beauty Services'}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2 text-sm text-gray-600 mb-4">
                        <p>{shop.address}</p>
                        <p>{shop.phone}</p>
                        <p>{shop.email}</p>
                      </div>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex-1"
                          onClick={() => handleViewShop(shop.id)}
                        >
                          <Eye className="w-4 h-4 mr-1" />
                          View
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex-1"
                          onClick={() => handleEditShop(shop.id)}
                        >
                          <Edit className="w-4 h-4 mr-1" />
                          Edit
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex-1"
                          onClick={() => handleManageShop(shop.id)}
                        >
                          <Settings className="w-4 h-4 mr-1" />
                          Manage
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>

          {/* Recent Appointments */}
          {shops.length > 0 && (
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-6">Recent Appointments</h3>
              {appointmentsLoading ? (
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center py-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900 mx-auto"></div>
                      <p className="mt-2 text-gray-600">Loading appointments...</p>
                    </div>
                  </CardContent>
                </Card>
              ) : recentAppointments && recentAppointments.length > 0 ? (
                <Card>
                  <CardContent className="pt-6">
                    <div className="space-y-4">
                      {recentAppointments.slice(0, 5).map((appointment) => (
                        <div key={appointment.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div className="flex-1">
                            <p className="font-medium text-gray-900">
                              {appointment.customerName || appointment.guestFirstName + ' ' + appointment.guestLastName}
                            </p>
                            <p className="text-sm text-gray-600">
                              {appointment.serviceName} • {new Date(appointment.appointmentDateTime).toLocaleDateString()}
                            </p>
                          </div>
                          <div className="text-right">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              appointment.status === 'CONFIRMED' ? 'bg-green-100 text-green-800' :
                              appointment.status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' :
                              appointment.status === 'COMPLETED' ? 'bg-blue-100 text-blue-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {appointment.status}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                    <div className="mt-4 text-center">
                      <Button
                        variant="outline"
                        className="w-full"
                        onClick={handleViewAppointments}
                      >
                        View All Appointments
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <Card>
                  <CardContent className="pt-6 text-center">
                    <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">No recent appointments</p>
                  </CardContent>
                </Card>
              )}
            </div>
          )}

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Calendar className="w-5 h-5 mr-2 text-blue-600" />
                  Appointments
                </CardTitle>
                <CardDescription>
                  View and manage all appointments
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  className="w-full"
                  onClick={handleViewAppointments}
                >
                  View All Appointments
                </Button>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="w-5 h-5 mr-2 text-green-600" />
                  Employees
                </CardTitle>
                <CardDescription>
                  Manage your team members
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  className="w-full"
                  onClick={handleManageEmployees}
                >
                  Manage Employees
                </Button>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart3 className="w-5 h-5 mr-2 text-purple-600" />
                  Analytics
                </CardTitle>
                <CardDescription>
                  Business insights and reports
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  className="w-full"
                  onClick={handleViewAnalytics}
                >
                  View Analytics
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>

      {/* Shop Creation Modal */}
      <ShopCreationModal
        isOpen={showShopCreationModal}
        onClose={() => setShowShopCreationModal(false)}
        onSuccess={handleShopCreationSuccess}
      />
    </div>
  )
}

export default OwnerDashboard
