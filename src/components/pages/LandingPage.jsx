import React, { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { ArrowRight, Calendar, MapPin, Star, Users } from 'lucide-react'
import { But<PERSON> } from '../ui/Button'
import useAuthStore from '../../store/authStore'
import ShopCreationModal from '../shop/ShopCreationModal'
import { Modal } from '../ui/Modal'
import LoginForm from '../auth/LoginForm'
import RegisterForm from '../auth/RegisterForm'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card'

const LandingPage = () => {
  const { isAuthenticated } = useAuthStore()
  const navigate = useNavigate()
  const [showShopCreationModal, setShowShopCreationModal] = useState(false)
  const [showLoginModal, setShowLoginModal] = useState(false)
  const [showRegisterModal, setShowRegisterModal] = useState(false)

  const handleJoinAsProfessional = () => {
    if (isAuthenticated) {
      setShowShopCreationModal(true)
    } else {
      setShowRegisterModal(true)
    }
  }

  const handleAuthSuccess = () => {
    setShowLoginModal(false)
    setShowRegisterModal(false)
    setShowShopCreationModal(true)
  }

  const handleShopCreationSuccess = async (data) => {

    try {
      // Close the modal first
      setShowShopCreationModal(false)

      // Show success message

      // Redirect to dashboard - the auth system will handle role updates naturally
      navigate('/dashboard')

    } catch (error) {
      console.error('Error in shop creation success handler:', error)
      setShowShopCreationModal(false)
      // Fallback: still redirect to dashboard as shop creation was successful
      navigate('/dashboard')
    }
  }

  const switchToLogin = () => {
    setShowRegisterModal(false)
    setShowLoginModal(true)
  }

  const switchToRegister = () => {
    setShowLoginModal(false)
    setShowRegisterModal(true)
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-50 to-white">
      {/* Hero Section */}
      <section className="relative py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            Welcome to BeautyHub
          </h1>
          <p className="text-xl md:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Discover and book appointments at the best beauty shops in your area. 
            From haircuts to spa treatments, find your perfect beauty experience.
          </p>
          <Button asChild size="lg" className="text-lg px-8 py-4 bg-gray-900 hover:bg-gray-800 text-white">
            <Link to="/search" className="flex items-center">
              Start Now
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Why Choose BeautyHub?
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              We make it easy to find and book beauty services that fit your style and schedule.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <Card className="text-center">
              <CardHeader>
                <div className="mx-auto w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                  <MapPin className="h-6 w-6 text-primary" />
                </div>
                <CardTitle className="text-xl">Find Nearby</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Discover beauty shops and services in your area with our location-based search.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="mx-auto w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                  <Calendar className="h-6 w-6 text-primary" />
                </div>
                <CardTitle className="text-xl">Easy Booking</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Book appointments instantly with real-time availability and confirmation.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="mx-auto w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                  <Star className="h-6 w-6 text-primary" />
                </div>
                <CardTitle className="text-xl">Quality Service</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Read reviews and ratings from real customers to choose the best services.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="mx-auto w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                  <Users className="h-6 w-6 text-primary" />
                </div>
                <CardTitle className="text-xl">Trusted Network</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Connect with verified beauty professionals and trusted establishments.
                </CardDescription>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-primary/5">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Ready to Transform Your Look?
          </h2>
          <p className="text-lg text-gray-600 mb-8">
            Join thousands of satisfied customers who have found their perfect beauty experience through BeautyHub.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" className="bg-gray-900 hover:bg-gray-800 text-white">
              <Link to="/search">Browse Shops</Link>
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="border-gray-900 text-gray-900 hover:bg-gray-900 hover:text-white"
              onClick={handleJoinAsProfessional}
            >
              Join as Professional
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-lg font-semibold mb-4">BeautyHub</h3>
              <p className="text-gray-400">
                Your trusted platform for beauty appointments and services.
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-4">For Customers</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link to="/search" className="hover:text-white">Find Shops</Link></li>
                <li><Link to="/register" className="hover:text-white">Sign Up</Link></li>
                <li><Link to="/help" className="hover:text-white">Help Center</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">For Professionals</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link to="/register" className="hover:text-white">Join BeautyHub</Link></li>
                <li><Link to="/business" className="hover:text-white">Business Tools</Link></li>
                <li><Link to="/support" className="hover:text-white">Support</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Company</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link to="/about" className="hover:text-white">About Us</Link></li>
                <li><Link to="/contact" className="hover:text-white">Contact</Link></li>
                <li><Link to="/privacy" className="hover:text-white">Privacy Policy</Link></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 BeautyHub. All rights reserved.</p>
          </div>
        </div>
      </footer>

      {/* Modals */}
      <ShopCreationModal
        isOpen={showShopCreationModal}
        onClose={() => setShowShopCreationModal(false)}
        onSuccess={handleShopCreationSuccess}
      />

      <Modal
        isOpen={showLoginModal}
        onClose={() => setShowLoginModal(false)}
        title="Sign In to BeautyHub"
      >
        <LoginForm
          onSuccess={handleAuthSuccess}
          onSwitchToRegister={switchToRegister}
          isModal={true}
        />
      </Modal>

      <Modal
        isOpen={showRegisterModal}
        onClose={() => setShowRegisterModal(false)}
        title="Join BeautyHub"
      >
        <RegisterForm
          onSuccess={handleAuthSuccess}
          onSwitchToLogin={switchToLogin}
          isModal={true}
        />
      </Modal>
    </div>
  )
}

export default LandingPage
