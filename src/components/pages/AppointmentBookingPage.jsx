import React, { useState, useEffect, useRef } from 'react'
import { useParams, useNavigate, Link } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { Calendar, Clock, User, Mail, Phone, CreditCard, ArrowLeft, Building } from 'lucide-react'
import useAuthStore from '../../store/authStore'
import { shopAPI, serviceAPI, employeeAPI } from '../../lib/api'
import { Button } from '../ui/Button'
import { Input } from '../ui/Input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card'
import { Alert, AlertDescription } from '../ui/Alert'
import { Badge } from '../ui/Badge'
import AppointmentBooking from '../appointments/AppointmentBooking'

const AppointmentBookingPage = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const { isAuthenticated, user } = useAuthStore()

  const [selectedService, setSelectedService] = useState(null)
  const [selectedEmployee, setSelectedEmployee] = useState(null)
  const [currentStep, setCurrentStep] = useState(1) // 1: Service, 2: Employee, 3: Booking
  const [isBookingSuccessful, setIsBookingSuccessful] = useState(false)
  const appointmentBookingRef = useRef(null)

  // Fetch shop data from API
  const { data: shopData, isLoading: shopLoading, error: shopError } = useQuery({
    queryKey: ['shop', id],
    queryFn: () => shopAPI.getShop(id),
    enabled: !!id
  })

  // Fetch shop employees (public endpoint for appointment booking)
  const { data: employeesData, isLoading: employeesLoading, error: employeesError } = useQuery({
    queryKey: ['shopEmployees', id],
    queryFn: () => shopAPI.getShopEmployees(id),
    enabled: !!id
  })

  const shop = shopData?.data

  // Handle different response structures
  let employees = []
  if (employeesData?.data) {
    if (Array.isArray(employeesData.data)) {
      employees = employeesData.data
    } else if (employeesData.data.data && Array.isArray(employeesData.data.data)) {
      // Handle nested data structure
      employees = employeesData.data.data
    } else if (typeof employeesData.data === 'object') {
      // Handle single employee object
      employees = [employeesData.data]
    }
  }

  // Extract unique services from all employees
  const services = employees.reduce((allServices, employee) => {
    if (employee.services && Array.isArray(employee.services)) {
      employee.services.forEach(service => {
        // Only add if not already in the list (avoid duplicates)
        if (!allServices.find(s => s.id === service.id)) {
          allServices.push({
            ...service,
            employeeId: employee.id,
            employeeName: employee.name
          })
        }
      })
    } else {
    }
    return allServices
  }, [])


  // Filter employees by selected service
  const availableEmployees = selectedService
    ? employees.filter(emp => emp.services?.some(service => service.id === selectedService.id))
    : employees

  const handleServiceSelect = (service) => {
    setSelectedService(service)
    setSelectedEmployee(null) // Reset employee selection when service changes
    setCurrentStep(2)
  }

  const handleEmployeeSelect = (employee) => {
    setSelectedEmployee(employee)
    setCurrentStep(3)
  }

  const handleRandomEmployeeSelect = () => {
    if (availableEmployees.length > 0) {
      const randomIndex = Math.floor(Math.random() * availableEmployees.length)
      const randomEmployee = availableEmployees[randomIndex]
      setSelectedEmployee(randomEmployee)
      setCurrentStep(3)
    }
  }

  const handleAppointmentSuccess = (appointment) => {
    // Don't navigate automatically - let the user choose what to do next
    // The BookingResultStep component will handle showing the success message
    // and provide navigation options
  }

  const handleBookingStateChange = (isSuccessful) => {
    setIsBookingSuccessful(isSuccessful)
  }

  const handleBack = async () => {
    // Prevent navigation if booking is successful
    if (isBookingSuccessful) {
      return
    }

    if (currentStep === 3) {
      // Release any locked slot when going back from booking to employee selection
      if (appointmentBookingRef.current?.releaseLockedSlot) {
        await appointmentBookingRef.current.releaseLockedSlot()
      }
      setCurrentStep(2)
    } else if (currentStep === 2) {
      setCurrentStep(1)
      setSelectedEmployee(null)
    }
  }

  if (shopLoading || employeesLoading) {
    return (
      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 rounded w-1/3"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="h-32 bg-gray-200 rounded"></div>
                <div className="h-48 bg-gray-200 rounded"></div>
              </div>
              <div className="h-64 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (shopError || !shop) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="text-center p-8">
          <CardContent>
            <h2 className="text-2xl font-bold mb-4">Shop Not Found</h2>
            <p className="text-gray-600 mb-4">The shop you're trying to book with doesn't exist.</p>
            <Button asChild className="bg-gray-900 hover:bg-gray-800 text-white">
              <Link to="/search">Browse Other Shops</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }



  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            {!isBookingSuccessful && (
              <Button
                variant="outline"
                onClick={() => navigate(`/shop/${id}`)}
                className="mr-4"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Shop
              </Button>
            )}
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Book Appointment</h1>
              <div className="flex items-center text-gray-600 mt-1">
                <Building className="w-4 h-4 mr-1" />
                <span>at {shop?.name}</span>
              </div>
            </div>
          </div>

          {/* Progress Steps */}
          <div className="flex items-center space-x-4 mb-6">
            <div className={`flex items-center ${currentStep >= 1 ? 'text-blue-600' : 'text-gray-400'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                currentStep >= 1 ? 'bg-blue-600 text-white' : 'bg-gray-200'
              }`}>
                1
              </div>
              <span className="ml-2 font-medium">Select Service</span>
            </div>
            <div className="flex-1 h-px bg-gray-300"></div>
            <div className={`flex items-center ${currentStep >= 2 ? 'text-blue-600' : 'text-gray-400'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                currentStep >= 2 ? 'bg-blue-600 text-white' : 'bg-gray-200'
              }`}>
                2
              </div>
              <span className="ml-2 font-medium">Choose Employee</span>
            </div>
            <div className="flex-1 h-px bg-gray-300"></div>
            <div className={`flex items-center ${currentStep >= 3 ? 'text-blue-600' : 'text-gray-400'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                currentStep >= 3 ? 'bg-blue-600 text-white' : 'bg-gray-200'
              }`}>
                3
              </div>
              <span className="ml-2 font-medium">Book Appointment</span>
            </div>
          </div>
        </div>

        {/* Step Content */}
        {currentStep === 1 && (
          <div className="max-w-4xl mx-auto">
            <Card>
              <CardHeader>
                <CardTitle>Select a Service</CardTitle>
                <CardDescription>Choose the service you'd like to book</CardDescription>
              </CardHeader>
              <CardContent>

                {services.length === 0 ? (
                  <Alert>
                    <AlertDescription>
                      This shop doesn't have any services available for booking yet.
                    </AlertDescription>
                  </Alert>
                ) : (
                  <div className="grid gap-4 md:grid-cols-2">
                    {services.map(service => (
                      <div
                        key={service.id}
                        className="p-4 border rounded-lg cursor-pointer transition-all hover:shadow-md hover:border-blue-300"
                        onClick={() => handleServiceSelect(service)}
                      >
                        <div className="flex justify-between items-start mb-2">
                          <h4 className="font-medium text-lg">{service.name}</h4>
                          <Badge variant="secondary">${service.price}</Badge>
                        </div>
                        {service.description && (
                          <p className="text-sm text-gray-600 mb-2">{service.description}</p>
                        )}
                        <div className="flex items-center text-sm text-gray-500">
                          <Clock className="w-4 h-4 mr-1" />
                          <span>{service.durationMinutes} minutes</span>
                          {service.employeeName && (
                            <>
                              <span className="mx-2">•</span>
                              <User className="w-4 h-4 mr-1" />
                              <span>with {service.employeeName}</span>
                            </>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        {currentStep === 2 && selectedService && (
          <div className="max-w-4xl mx-auto">
            {!isBookingSuccessful && (
              <div className="mb-6">
                <Button
                  variant="outline"
                  onClick={handleBack}
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Services
                </Button>
              </div>
            )}
            <Card>
              <CardHeader>
                <CardTitle>Choose Your Stylist</CardTitle>
                <CardDescription>
                  Select who you'd like to perform your {selectedService.name}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {availableEmployees.length === 0 ? (
                  <Alert>
                    <AlertDescription>
                      No employees are available for this service. Please contact the shop directly.
                    </AlertDescription>
                  </Alert>
                ) : (
                  <div className="space-y-4">
                    {/* Random Employee Option - Show if more than 2 employees */}
                    {availableEmployees.length > 2 && (
                      <div
                        className="p-4 border-2 border-dashed border-blue-300 rounded-lg cursor-pointer transition-all hover:shadow-md hover:border-blue-500 hover:bg-blue-50"
                        onClick={handleRandomEmployeeSelect}
                      >
                        <div className="flex items-center justify-center space-x-3">
                          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                            <User className="w-6 h-6 text-blue-600" />
                          </div>
                          <div className="text-center">
                            <h4 className="font-medium text-blue-900">Any Available Stylist</h4>
                            <p className="text-sm text-blue-600 mt-1">
                              Let us choose the best available stylist for you
                            </p>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Individual Employee Options */}
                    <div className="grid gap-4 md:grid-cols-2">
                      {availableEmployees.map(employee => (
                        <div
                          key={employee.id}
                          className="p-4 border rounded-lg cursor-pointer transition-all hover:shadow-md hover:border-blue-300"
                          onClick={() => handleEmployeeSelect(employee)}
                        >
                          <div className="flex items-start space-x-4">
                            <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                              <User className="w-6 h-6 text-gray-400" />
                            </div>
                            <div className="flex-1">
                              <h4 className="font-medium text-lg">{employee.name}</h4>
                              {employee.bio && (
                                <p className="text-sm text-gray-600 mb-2">{employee.bio}</p>
                              )}
                              <div className="space-y-1 text-sm text-gray-500">
                                {employee.specialties && (
                                  <div>Specialties: {employee.specialties}</div>
                                )}
                                {employee.yearsExperience && (
                                  <div>{employee.yearsExperience} years experience</div>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        {currentStep === 3 && selectedService && selectedEmployee && (
          <div className="max-w-4xl mx-auto">
            {!isBookingSuccessful && (
              <div className="mb-6">
                <Button
                  variant="outline"
                  onClick={handleBack}
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Employee Selection
                </Button>
              </div>
            )}

            {/* Selection Summary */}
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>Booking Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">Service:</span>
                    <div className="text-gray-900">{selectedService.name}</div>
                    <div className="text-gray-500">{selectedService.durationMinutes} minutes</div>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Stylist:</span>
                    <div className="text-gray-900">{selectedEmployee.name}</div>
                    {selectedEmployee.specialties && (
                      <div className="text-gray-500">{selectedEmployee.specialties}</div>
                    )}
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Price:</span>
                    <div className="text-gray-900 font-semibold">${selectedService.price}</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Appointment Booking Component */}
            <AppointmentBooking
              ref={appointmentBookingRef}
              shopId={id}
              serviceId={selectedService.id}
              employeeId={selectedEmployee.id}
              onSuccess={handleAppointmentSuccess}
              onBookingStateChange={handleBookingStateChange}
            />
          </div>
        )}

      </div>
    </div>
  )
}

export default AppointmentBookingPage
