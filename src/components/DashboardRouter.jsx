import React, { useEffect, useState } from 'react'
import useAuthStore from '../store/authStore'
import Dashboard from './Dashboard'
import OwnerDashboard from './OwnerDashboard'
import EmployeeDashboard from './EmployeeDashboard'

const DashboardRouter = () => {
  const { isOwner, isEmployee, refreshToken, validateToken } = useAuthStore()
  const [isLoading, setIsLoading] = useState(true)
  const [userRole, setUserRole] = useState(null)

  useEffect(() => {
    const checkUserRole = async () => {
      try {
        // Try to refresh token to get latest role data
        await refreshToken()

        // If refresh fails, try validation
        await validateToken()

        const currentRole = isOwner() ? 'OWNER' : isEmployee() ? 'EMPLOYEE' : 'USER'
        setUserRole(currentRole)
      } catch (error) {
        console.error('DashboardRouter: Error checking user role:', error)
        // Default to checking current state
        setUserRole(isOwner() ? 'OWNER' : isEmployee() ? 'EMPLOYEE' : 'USER')
      } finally {
        setIsLoading(false)
      }
    }

    checkUserRole()
  }, [isOwner, isEmployee, refreshToken, validateToken])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  // Route to appropriate dashboard based on user role
  if (userRole === 'OWNER' || isOwner()) {
    return <OwnerDashboard />
  }

  if (userRole === 'EMPLOYEE' || isEmployee()) {
    return <EmployeeDashboard />
  }

  // Default to general dashboard for regular users
  return <Dashboard />
}

export default DashboardRouter
