import React, { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { serviceAPI, employeeAPI, shopAPI } from '../../lib/api'
import { Button } from '../ui/Button'
import { Input } from '../ui/Input'
import Textarea from '../ui/Textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card'
import { Alert, AlertDescription } from '../ui/Alert'
import { Modal } from '../ui/Modal'
import { Plus, Edit, Trash2, DollarSign, Clock, User } from 'lucide-react'

const ServiceManagement = ({ shopId }) => {
  const queryClient = useQueryClient()
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [editingService, setEditingService] = useState(null)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    durationMinutes: '',
    depositAmount: '',
    employeeId: ''
  })
  const [errors, setErrors] = useState({})

  // Fetch shop services
  const { data: services, isLoading: servicesLoading, error: servicesError } = useQuery({
    queryKey: ['shopServices', shopId],
    queryFn: () => {
      return shopAPI.getShopServices(shopId)
    },
    enabled: !!shopId
  })



  // Fetch shop employees for assignment
  const { data: employees, isLoading: employeesLoading } = useQuery({
    queryKey: ['shopEmployees', shopId],
    queryFn: () => employeeAPI.getShopEmployees(shopId),
    enabled: !!shopId
  })

  // Create service mutation
  const createServiceMutation = useMutation({
    mutationFn: serviceAPI.createService,
    onSuccess: () => {
      queryClient.invalidateQueries(['shopServices', shopId])
      setShowCreateModal(false)
      setFormData({
        name: '',
        description: '',
        price: '',
        durationMinutes: '',
        depositAmount: '',
        employeeId: ''
      })
      setErrors({})
    },
    onError: (error) => {
      setErrors({ general: error.response?.data?.message || 'Failed to create service' })
    }
  })

  // Update service mutation
  const updateServiceMutation = useMutation({
    mutationFn: ({ id, ...data }) => serviceAPI.updateService(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries(['shopServices', shopId])
      setEditingService(null)
      setFormData({
        name: '',
        description: '',
        price: '',
        durationMinutes: '',
        depositAmount: '',
        employeeId: ''
      })
      setErrors({})
    },
    onError: (error) => {
      setErrors({ general: error.response?.data?.message || 'Failed to update service' })
    }
  })

  // Delete service mutation
  const deleteServiceMutation = useMutation({
    mutationFn: serviceAPI.deleteService,
    onSuccess: () => {
      queryClient.invalidateQueries(['shopServices', shopId])
    },
    onError: (error) => {
      setErrors({ general: error.response?.data?.message || 'Failed to delete service' })
    }
  })

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }))
    }
  }

  const validateForm = () => {
    const newErrors = {}
    
    if (!formData.name.trim()) newErrors.name = 'Service name is required'
    if (!formData.price || parseFloat(formData.price) <= 0) newErrors.price = 'Valid price is required'
    if (!formData.durationMinutes || parseInt(formData.durationMinutes) <= 0) newErrors.durationMinutes = 'Valid duration is required'
    if (!formData.employeeId) newErrors.employeeId = 'Employee assignment is required'

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    if (!validateForm()) return

    const serviceData = {
      ...formData,
      shopId,
      price: parseFloat(formData.price),
      durationMinutes: parseInt(formData.durationMinutes),
      depositAmount: formData.depositAmount ? parseFloat(formData.depositAmount) : 0
    }

    if (editingService) {
      updateServiceMutation.mutate({ id: editingService.id, ...serviceData })
    } else {
      createServiceMutation.mutate(serviceData)
    }
  }

  const handleEdit = (service) => {
    setEditingService(service)
    setFormData({
      name: service.name,
      description: service.description || '',
      price: service.price.toString(),
      durationMinutes: service.durationMinutes.toString(),
      depositAmount: service.depositAmount?.toString() || '',
      employeeId: service.employeeId
    })
    setShowCreateModal(true)
  }

  const handleDelete = (serviceId) => {
    if (window.confirm('Are you sure you want to delete this service?')) {
      deleteServiceMutation.mutate(serviceId)
    }
  }

  const resetForm = () => {
    setEditingService(null)
    setFormData({
      name: '',
      description: '',
      price: '',
      durationMinutes: '',
      depositAmount: '',
      employeeId: ''
    })
    setErrors({})
  }

  if (servicesLoading || employeesLoading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (servicesError) {
    console.error('ServiceManagement: Services error:', servicesError)
    return (
      <Alert variant="destructive">
        <AlertDescription>
          Failed to load services. {servicesError?.response?.data?.message || servicesError?.message || 'Please try again later.'}
        </AlertDescription>
      </Alert>
    )
  }

  // Handle the case where services might be undefined or not have the expected structure
  // Backend returns {data: [...]} and Axios wraps it in response.data, so we need services.data.data
  const servicesList = Array.isArray(services?.data?.data) ? services.data.data : []
  const employeesList = Array.isArray(employees?.data?.data) ? employees.data.data : []

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Service Management</h2>
          <p className="text-gray-600">Create and manage services for your shop</p>
        </div>
        <Button onClick={() => setShowCreateModal(true)} className="flex items-center">
          <Plus className="w-4 h-4 mr-2" />
          Add Service
        </Button>
      </div>

      {errors.general && (
        <Alert variant="destructive">
          <AlertDescription>{errors.general}</AlertDescription>
        </Alert>
      )}

      <div className="grid gap-4">
        {servicesList.length === 0 ? (
          <Card>
            <CardContent className="p-6 text-center">
              <p className="text-gray-500 mb-4">No services created yet.</p>
              <Button onClick={() => setShowCreateModal(true)}>Create Your First Service</Button>
            </CardContent>
          </Card>
        ) : (
          servicesList.map((service) => (
            <Card key={service.id}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg">{service.name}</CardTitle>
                    <CardDescription>{service.description}</CardDescription>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" onClick={() => handleEdit(service)}>
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => handleDelete(service.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div className="flex items-center">
                    <DollarSign className="w-4 h-4 mr-2 text-green-600" />
                    <span>${service.price}</span>
                  </div>
                  <div className="flex items-center">
                    <Clock className="w-4 h-4 mr-2 text-blue-600" />
                    <span>{service.durationMinutes} min</span>
                  </div>
                  <div className="flex items-center">
                    <User className="w-4 h-4 mr-2 text-purple-600" />
                    <span>{service.employeeName || 'Unassigned'}</span>
                  </div>
                  {service.depositAmount > 0 && (
                    <div className="flex items-center">
                      <span className="text-gray-600">Deposit: ${service.depositAmount}</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Create/Edit Service Modal */}
      <Modal
        isOpen={showCreateModal}
        onClose={() => {
          setShowCreateModal(false)
          resetForm()
        }}
        title={editingService ? 'Edit Service' : 'Create New Service'}
      >
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Service Name *
            </label>
            <Input
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="e.g., Haircut, Manicure, Facial"
              error={errors.name}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <Textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Describe the service..."
              rows={3}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Price ($) *
              </label>
              <Input
                type="number"
                step="0.01"
                min="0"
                value={formData.price}
                onChange={(e) => handleInputChange('price', e.target.value)}
                placeholder="0.00"
                error={errors.price}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Duration (minutes) *
              </label>
              <Input
                type="number"
                min="1"
                value={formData.durationMinutes}
                onChange={(e) => handleInputChange('durationMinutes', e.target.value)}
                placeholder="60"
                error={errors.durationMinutes}
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Deposit Amount ($)
            </label>
            <Input
              type="number"
              step="0.01"
              min="0"
              value={formData.depositAmount}
              onChange={(e) => handleInputChange('depositAmount', e.target.value)}
              placeholder="0.00 (optional)"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Assign to Employee *
            </label>
            <select
              value={formData.employeeId}
              onChange={(e) => handleInputChange('employeeId', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select an employee</option>
              {employeesList.map((employee) => (
                <option key={employee.id} value={employee.id}>
                  {employee.name}
                </option>
              ))}
            </select>
            {errors.employeeId && (
              <p className="text-red-500 text-sm mt-1">{errors.employeeId}</p>
            )}
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setShowCreateModal(false)
                resetForm()
              }}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={createServiceMutation.isLoading || updateServiceMutation.isLoading}
            >
              {editingService ? 'Update Service' : 'Create Service'}
            </Button>
          </div>
        </form>
      </Modal>
    </div>
  )
}

export default ServiceManagement
